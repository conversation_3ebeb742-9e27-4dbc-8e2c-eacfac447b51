package models

import "time"

type TaxRate struct {
	Id          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Code        *string   `json:"code" gorm:"type:varchar(255)"`
	Type        *string   `json:"type" gorm:"type:varchar(255);default:'percentage'"`
	Amount      float64   `json:"amount"`
	Active      bool      `json:"active" gorm:"default:true"`
	Description *string   `json:"description" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type TaxRateRequest struct {
	Name        string  `json:"name" binding:"required"`
	Code        *string `json:"code"`
	Type        *string `json:"type"`
	Amount      float64 `json:"amount" binding:"required"`
	Active      bool    `json:"active"`
	Description *string `json:"description"`
}
