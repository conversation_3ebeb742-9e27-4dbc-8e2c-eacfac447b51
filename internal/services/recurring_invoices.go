package services

import (
	"bytes"
	"fmt"
	"github.com/<PERSON>bas<PERSON>an<PERSON><PERSON><PERSON>/go-wkhtmltopdf"
	"gorm.io/gorm"
	"html/template"
	"log"
	"strconv"
	"time"
	"yotracker/cmd/web/backend/controllers"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

type RecurringInvoiceService struct{}

func NewRecurringInvoiceService() *RecurringInvoiceService {
	return &RecurringInvoiceService{}
}

// ProcessDailyRecurringInvoices processes all recurring invoices that are due for generation
func (r *RecurringInvoiceService) ProcessDailyRecurringInvoices() error {
	// Get the generate_invoice_before_days setting
	generateBeforeDays := models.GetSetting("generate_invoice_before_days")
	if generateBeforeDays == "" {
		generateBeforeDays = "7" // default value
	}

	beforeDaysInt, err := strconv.Atoi(generateBeforeDays)
	if err != nil {
		return fmt.Errorf("invalid generate_invoice_before_days setting: %v", err)
	}

	// Calculate the target date (today + generate_invoice_before_days)
	targetDate := time.Now().AddDate(0, 0, beforeDaysInt)

	log.Printf("Processing recurring invoices due on: %s, based on generate_invoice_before_days setting of %s days", targetDate.Format("2006-01-02"), generateBeforeDays)

	// Find all recurring invoices that need to be processed. We do not generate a new one if invoice balance is greater than 0
	var invoices []models.Invoice
	err = config.DB.Where("recurring = ? AND recur_next_date <= ? AND recur_next_date IS NOT NULL and status = 'paid'", true, targetDate).
		Preload("Client").
		Preload("InvoiceItems").
		Preload("Currency").
		Preload("PaymentType").
		Find(&invoices).Error

	if err != nil {
		return fmt.Errorf("failed to fetch recurring invoices: %v", err)
	}

	log.Printf("Found %d recurring invoices to process", len(invoices))

	for _, invoice := range invoices {
		err := r.processRecurringInvoice(invoice)
		if err != nil {
			log.Printf("Failed to process recurring invoice ID %d: %v", invoice.Id, err)
			continue
		}
		log.Printf("Successfully processed recurring invoice ID %d", invoice.Id)
	}

	return nil
}

// processRecurringInvoice processes a single recurring invoice
func (r *RecurringInvoiceService) processRecurringInvoice(originalInvoice models.Invoice) error {
	// Get invoice_due_after_days setting
	dueDays := models.GetSetting("invoice_due_after_days")
	if dueDays == "" {
		dueDays = "15" // default value
	}

	dueDaysInt, err := strconv.Atoi(dueDays)
	if err != nil {
		return fmt.Errorf("invalid invoice_due_after_days setting: %v", err)
	}

	// Create new invoice based on the original
	dueDate := originalInvoice.RecurNextDate.AddDate(0, 0, dueDaysInt)

	newInvoice := models.Invoice{
		ClientId:             originalInvoice.ClientId,
		CreatedById:          originalInvoice.CreatedById,
		CouponId:             originalInvoice.CouponId,
		TaxRateId:            originalInvoice.TaxRateId,
		PaymentTypeId:        originalInvoice.PaymentTypeId,
		CurrencyId:           originalInvoice.CurrencyId,
		Date:                 originalInvoice.RecurNextDate,
		DueDate:              &dueDate,
		Amount:               originalInvoice.Amount,
		Discount:             originalInvoice.Discount,
		DiscountType:         originalInvoice.DiscountType,
		Status:               "draft",
		Balance:              originalInvoice.Amount,
		Xrate:                originalInvoice.Xrate,
		DiscountAmount:       originalInvoice.DiscountAmount,
		CouponDiscountAmount: originalInvoice.CouponDiscountAmount,
		TaxAmount:            originalInvoice.TaxAmount,
		AdminNotes:           originalInvoice.AdminNotes,
		Terms:                originalInvoice.Terms,
		Recurring:            originalInvoice.Recurring,
		RecurFrequency:       originalInvoice.RecurFrequency,
		RecurStartDate:       originalInvoice.RecurStartDate,
		RecurEndDate:         originalInvoice.RecurEndDate,
		Description:          originalInvoice.Description,
		IsSubscription:       originalInvoice.IsSubscription,
	}

	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create the new invoice
	if err := tx.Create(&newInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create new invoice: %v", err)
	}

	// Generate reference for the new invoice
	reference := utils.GenerateReference(strconv.Itoa(int(newInvoice.Id)))
	newInvoice.Reference = &reference
	if err := tx.Save(&newInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update invoice reference: %v", err)
	}

	// Handle invoice items based on subscription type
	if originalInvoice.IsSubscription != nil && *originalInvoice.IsSubscription {
		// For subscription invoices, get all ClientDevices for this client
		err = r.createSubscriptionInvoiceItems(tx, &newInvoice, originalInvoice.ClientId)
	} else {
		// For non-subscription invoices, copy existing items
		err = r.copyInvoiceItems(tx, newInvoice.Id, originalInvoice.InvoiceItems)
	}

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice items: %v", err)
	}

	// Calculate next recurring date
	nextRecurDate, err := r.calculateNextRecurringDate(originalInvoice)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to calculate next recurring date: %v", err)
	}

	// Update original invoice's next recurring date
	originalInvoice.RecurNextDate = &nextRecurDate
	if err := tx.Save(&originalInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update original invoice recurring date: %v", err)
	}
	controllers.UpdateInvoiceStatus(newInvoice.Id)

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	// Load the complete invoice for email sending
	var completeInvoice models.Invoice
	err = config.DB.Preload("Client").Preload("Client.Country").Preload("InvoiceItems").
		Preload("Currency").Preload("PaymentType").First(&completeInvoice, newInvoice.Id).Error
	if err != nil {
		return fmt.Errorf("failed to load complete invoice: %v", err)
	}
	return nil
	// Generate PDF and send email
	return r.generatePDFAndSendEmail(completeInvoice)
}

// createSubscriptionInvoiceItems creates invoice items from all ClientDevices for the client
func (r *RecurringInvoiceService) createSubscriptionInvoiceItems(tx *gorm.DB, invoice *models.Invoice, clientId uint) error {
	var clientDevices []models.ClientDevice
	err := tx.Where("client_id = ? AND status = ?", clientId, "active").Preload("DeviceType").Find(&clientDevices).Error
	if err != nil {
		return fmt.Errorf("failed to fetch client devices: %v", err)
	}

	for i, device := range clientDevices {
		deviceName := "Device"
		if device.Name != nil {
			deviceName = *device.Name
			if device.PlateNumber != nil {
				deviceName += " (" + *device.PlateNumber + ")"
			}
		}

		deviceAmount := float64(0)
		if device.DeviceType.Amount != nil {
			deviceAmount = *device.DeviceType.Amount
		}
		xrate := *invoice.Xrate
		deviceAmount = deviceAmount * xrate

		invoiceItem := models.InvoiceItem{
			InvoiceId:            invoice.Id,
			ClientDeviceId:       &device.Id,
			Name:                 &deviceName,
			Description:          device.Description,
			Quantity:             func() *uint { q := uint(1); return &q }(),
			ItemPosition:         func() *uint { pos := uint(i + 1); return &pos }(),
			UnitCost:             &deviceAmount,
			Total:                &deviceAmount,
			BaseCurrencyUnitCost: &deviceAmount,
		}

		if err := tx.Create(&invoiceItem).Error; err != nil {
			return fmt.Errorf("failed to create invoice item for device %d: %v", device.Id, err)
		}
	}

	return nil
}

// copyInvoiceItems copies invoice items from the original invoice
func (r *RecurringInvoiceService) copyInvoiceItems(tx *gorm.DB, newInvoiceId uint, originalItems []models.InvoiceItem) error {
	for _, item := range originalItems {
		newItem := models.InvoiceItem{
			InvoiceId:            newInvoiceId,
			ClientDeviceId:       item.ClientDeviceId,
			TaxRateId:            item.TaxRateId,
			Name:                 item.Name,
			Description:          item.Description,
			Quantity:             item.Quantity,
			ItemPosition:         item.ItemPosition,
			UnitCost:             item.UnitCost,
			BaseCurrencyUnitCost: item.BaseCurrencyUnitCost,
			Discount:             item.Discount,
			DiscountType:         item.DiscountType,
			DiscountAmount:       item.DiscountAmount,
			Total:                item.Total,
		}

		if err := tx.Create(&newItem).Error; err != nil {
			return fmt.Errorf("failed to copy invoice item: %v", err)
		}
	}

	return nil
}

// calculateNextRecurringDate calculates the next recurring date using client billing cycle
func (r *RecurringInvoiceService) calculateNextRecurringDate(invoice models.Invoice) (time.Time, error) {
	var client models.Client
	err := config.DB.First(&client, invoice.ClientId).Error
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to fetch client: %v", err)
	}

	if client.BillingCycle == nil {
		return time.Time{}, fmt.Errorf("client billing cycle not set")
	}

	billingCycle := *client.BillingCycle
	billingDay := uint(5) // default billing day
	if client.BillingDay != nil {
		billingDay = *client.BillingDay
	}

	// Use the existing billing date calculation logic from invoices controller
	return calculateNextBillingDate(*invoice.RecurNextDate, billingCycle, billingDay), nil
}

// generatePDFAndSendEmail generates PDF and sends email using existing functionality
func (r *RecurringInvoiceService) generatePDFAndSendEmail(invoice models.Invoice) error {
	// Generate PDF using the same logic as in frontend controller
	tmpl, err := template.ParseFiles("templates/invoice.html")
	if err != nil {
		return fmt.Errorf("template error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		return fmt.Errorf("template render error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return fmt.Errorf("PDF generator error: %v", err)
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return fmt.Errorf("PDF creation failed: %v", err)
	}

	// Send email with PDF attachment using existing helper
	return mail.SendInvoiceEmail(&invoice, pdfg.Bytes())
}

// calculateNextBillingDate calculates the next billing date based on invoice date, billing cycle and billing day
// This is copied from the invoices controller to maintain consistency
func calculateNextBillingDate(invoiceDate time.Time, billingCycle string, billingDay uint) time.Time {
	switch billingCycle {
	case "monthly":
		return calculateNextMonthlyDate(invoiceDate, int(billingDay))
	case "quarterly":
		return calculateNextQuarterlyDate(invoiceDate, int(billingDay))
	case "half_yearly":
		return calculateNextHalfYearlyDate(invoiceDate, int(billingDay))
	case "yearly":
		return calculateNextYearlyDate(invoiceDate, int(billingDay))
	default:
		// Default to monthly if unknown cycle
		return calculateNextMonthlyDate(invoiceDate, int(billingDay))
	}
}

// calculateNextMonthlyDate calculates the next monthly billing date
func calculateNextMonthlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Create the target date for next month
	nextMonth := month + 1
	nextYear := year
	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}

	targetDate := time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in next month (e.g., Feb 30), use the last day of the month
	if targetDate.Month() != nextMonth {
		targetDate = time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, from.Location()) // Last day of next month
	}

	return targetDate
}

// calculateNextQuarterlyDate calculates the next quarterly billing date (every 3 months)
func calculateNextQuarterlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next quarter month
	var nextQuarterMonth time.Month
	switch {
	case month <= 3:
		nextQuarterMonth = 6
	case month <= 6:
		nextQuarterMonth = 9
	case month <= 9:
		nextQuarterMonth = 12
	case month <= 12:
		nextQuarterMonth = 3
		year++
	}

	targetDate := time.Date(year, nextQuarterMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextQuarterMonth {
		targetDate = time.Date(year, nextQuarterMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	return targetDate
}

// calculateNextHalfYearlyDate calculates the next half-yearly billing date (every 6 months)
func calculateNextHalfYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next half-year month (June or December)
	var nextHalfYearMonth time.Month
	if month <= 6 {
		nextHalfYearMonth = 12
	} else {
		nextHalfYearMonth = 6
		year++
	}

	targetDate := time.Date(year, nextHalfYearMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextHalfYearMonth {
		targetDate = time.Date(year, nextHalfYearMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	return targetDate
}

// calculateNextYearlyDate calculates the next yearly billing date
func calculateNextYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Move to next year
	year++
	targetDate := time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in this month, use the last day of the month
	if targetDate.Month() != month {
		targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location())
	}

	return targetDate
}
