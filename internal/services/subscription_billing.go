package services

import (
	"fmt"
	"log"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"gorm.io/gorm"
)

// SubscriptionBillingService handles subscription billing based on client NextBillingDate
type SubscriptionBillingService struct{}

// NewSubscriptionBillingService creates a new subscription billing service
func NewSubscriptionBillingService() *SubscriptionBillingService {
	return &SubscriptionBillingService{}
}

// ProcessSubscriptionBilling processes subscription billing for clients based on NextBillingDate
func (s *SubscriptionBillingService) ProcessSubscriptionBilling() error {
	// Get the generate_invoice_before_days setting
	generateBeforeDays := models.GetSetting("generate_invoice_before_days")
	if generateBeforeDays == "" {
		generateBeforeDays = "7" // default value
	}

	beforeDaysInt, err := strconv.Atoi(generateBeforeDays)
	if err != nil {
		return fmt.Errorf("invalid generate_invoice_before_days setting: %v", err)
	}

	// Calculate the target date (today + generate_invoice_before_days)
	targetDate := time.Now().AddDate(0, 0, beforeDaysInt)

	log.Printf("Processing subscription billing for clients with NextBillingDate <= %s (generate_invoice_before_days: %s)", 
		targetDate.Format("2006-01-02"), generateBeforeDays)

	// Find all active clients where NextBillingDate <= targetDate
	var clients []models.Client
	err = config.DB.Where("status = ? AND next_billing_date <= ? AND next_billing_date IS NOT NULL", 
		"active", targetDate).
		Preload("Currency").
		Find(&clients).Error

	if err != nil {
		return fmt.Errorf("failed to fetch clients for subscription billing: %v", err)
	}

	log.Printf("Found %d clients for subscription billing", len(clients))

	for _, client := range clients {
		err := s.processClientSubscription(client)
		if err != nil {
			log.Printf("Failed to process subscription for client ID %d (%s): %v", client.Id, client.Name, err)
			continue
		}
		log.Printf("Successfully processed subscription for client ID %d (%s)", client.Id, client.Name)
	}

	return nil
}

// processClientSubscription processes subscription billing for a single client
func (s *SubscriptionBillingService) processClientSubscription(client models.Client) error {
	// Check if client has any unpaid invoices
	hasUnpaidInvoices, err := s.hasUnpaidInvoices(client.Id)
	if err != nil {
		return fmt.Errorf("failed to check unpaid invoices: %v", err)
	}

	if hasUnpaidInvoices {
		log.Printf("Client %s (ID: %d) has unpaid invoices, skipping subscription billing", client.Name, client.Id)
		return nil
	}

	// Get all active client devices
	var clientDevices []models.ClientDevice
	err = config.DB.Where("client_id = ? AND status = ?", client.Id, "active").
		Preload("DeviceType").
		Find(&clientDevices).Error

	if err != nil {
		return fmt.Errorf("failed to fetch client devices: %v", err)
	}

	if len(clientDevices) == 0 {
		log.Printf("Client %s (ID: %d) has no active devices, skipping subscription billing", client.Name, client.Id)
		return nil
	}

	// Create subscription invoice
	return s.createSubscriptionInvoice(client, clientDevices)
}

// hasUnpaidInvoices checks if client has any unpaid invoices (balance > 0)
func (s *SubscriptionBillingService) hasUnpaidInvoices(clientId uint) (bool, error) {
	var count int64
	err := config.DB.Model(&models.Invoice{}).
		Where("client_id = ? AND balance > 0 AND status != ?", clientId, "paid").
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// createSubscriptionInvoice creates a new subscription invoice for the client
func (s *SubscriptionBillingService) createSubscriptionInvoice(client models.Client, devices []models.ClientDevice) error {
	// Get invoice_due_after_days setting
	dueDays := models.GetSetting("invoice_due_after_days")
	if dueDays == "" {
		dueDays = "15" // default value
	}

	dueDaysInt, err := strconv.Atoi(dueDays)
	if err != nil {
		return fmt.Errorf("invalid invoice_due_after_days setting: %v", err)
	}

	// Calculate invoice date and due date
	invoiceDate := client.NextBillingDate
	dueDate := invoiceDate.AddDate(0, 0, dueDaysInt)

	// Get default payment type
	var paymentType models.PaymentType
	err = config.DB.Where("active = ?", true).First(&paymentType).Error
	if err != nil {
		return fmt.Errorf("failed to get default payment type: %v", err)
	}

	// Calculate total amount
	var totalAmount float64
	for _, device := range devices {
		if device.DeviceType.Amount != nil {
			totalAmount += *device.DeviceType.Amount
		}
	}

	// Create invoice first to get ID for reference generation
	invoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     client.CurrencyId,
		PaymentTypeId:  &paymentType.Id,
		Date:           invoiceDate,
		DueDate:        &dueDate,
		Amount:         &totalAmount,
		Balance:        &totalAmount,
		Status:         "draft",
		IsSubscription: func() *bool { b := true; return &b }(),
		Xrate:          func() *float64 { x := 1.0; return &x }(),
	}

	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create invoice
	if err := tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice: %v", err)
	}

	// Generate reference after creating invoice to get ID
	reference := utils.GenerateReference(strconv.Itoa(int(invoice.Id)))
	invoice.Reference = &reference
	if err := tx.Save(&invoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update invoice reference: %v", err)
	}

	// Create invoice items from devices
	err = s.createInvoiceItemsFromDevices(tx, &invoice, devices)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice items: %v", err)
	}

	// Update client's next billing date
	err = s.updateClientNextBillingDate(tx, &client)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update client next billing date: %v", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("Created subscription invoice %s for client %s with %d devices (Total: %.2f)",
		reference, client.Name, len(devices), totalAmount)

	return nil
}

// createInvoiceItemsFromDevices creates invoice items from client devices
func (s *SubscriptionBillingService) createInvoiceItemsFromDevices(tx *gorm.DB, invoice *models.Invoice, devices []models.ClientDevice) error {
	for i, device := range devices {
		// Get device amount from device type
		deviceAmount := 0.0
		if device.DeviceType.Amount != nil {
			deviceAmount = *device.DeviceType.Amount
		}

		// Create item name: device name + plate number if available
		itemName := device.DeviceType.Name
		if device.Name != nil && *device.Name != "" {
			itemName = *device.Name
		}
		if device.PlateNumber != nil && *device.PlateNumber != "" {
			itemName += " (" + *device.PlateNumber + ")"
		}

		// Create invoice item
		invoiceItem := models.InvoiceItem{
			InvoiceId:            invoice.Id,
			ClientDeviceId:       &device.Id,
			Name:                 &itemName,
			Description:          device.Description,
			Quantity:             func() *uint { q := uint(1); return &q }(),
			ItemPosition:         func() *uint { pos := uint(i + 1); return &pos }(),
			UnitCost:             &deviceAmount,
			Total:                &deviceAmount,
			BaseCurrencyUnitCost: &deviceAmount,
		}

		if err := tx.Create(&invoiceItem).Error; err != nil {
			return fmt.Errorf("failed to create invoice item for device %d: %v", device.Id, err)
		}
	}

	return nil
}

// updateClientNextBillingDate updates the client's next billing date based on billing cycle
func (s *SubscriptionBillingService) updateClientNextBillingDate(tx *gorm.DB, client *models.Client) error {
	if client.BillingCycle == nil {
		return fmt.Errorf("client billing cycle not set")
	}

	billingCycle := *client.BillingCycle
	billingDay := uint(5) // default billing day
	if client.BillingDay != nil {
		billingDay = *client.BillingDay
	}

	// Calculate next billing date
	nextBillingDate := s.calculateNextBillingDate(*client.NextBillingDate, billingCycle, billingDay)

	// Update client
	client.NextBillingDate = &nextBillingDate
	client.LastBilledAt = client.NextBillingDate

	if err := tx.Save(client).Error; err != nil {
		return fmt.Errorf("failed to update client billing dates: %v", err)
	}

	return nil
}

// calculateNextBillingDate calculates the next billing date based on current date, billing cycle and billing day
func (s *SubscriptionBillingService) calculateNextBillingDate(currentDate time.Time, billingCycle string, billingDay uint) time.Time {
	switch billingCycle {
	case "monthly":
		return s.calculateNextMonthlyDate(currentDate, int(billingDay))
	case "quarterly":
		return s.calculateNextQuarterlyDate(currentDate, int(billingDay))
	case "half_yearly":
		return s.calculateNextHalfYearlyDate(currentDate, int(billingDay))
	case "yearly":
		return s.calculateNextYearlyDate(currentDate, int(billingDay))
	default:
		// Default to monthly if unknown cycle
		return s.calculateNextMonthlyDate(currentDate, int(billingDay))
	}
}

// Helper functions for calculating next billing dates
func (s *SubscriptionBillingService) calculateNextMonthlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 1
	nextYear := year

	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}

	// Handle cases where billing day doesn't exist in the target month
	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextQuarterlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 3

	nextYear := year
	if nextMonth > 12 {
		nextMonth -= 12
		nextYear++
	}

	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextHalfYearlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 6

	nextYear := year
	if nextMonth > 12 {
		nextMonth -= 12
		nextYear++
	}

	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextYearlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextYear := year + 1

	daysInMonth := time.Date(nextYear, month+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, month, billingDay, 0, 0, 0, 0, time.UTC)
}
