package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupTaxRateTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()

	gin.SetMode(gin.TestMode)
}

func TestTaxRatesCRUD(t *testing.T) {
	// Setup test database
	setupTaxRateTest()

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add test routes
	v1 := router.Group("/api/v1/backend")
	taxRates := v1.Group("/tax_rates")
	taxRates.GET("", GetAllTaxRates)
	taxRates.GET("/search", SearchTaxRates)
	taxRates.GET("/:id", GetTaxRateById)
	taxRates.POST("", CreateTaxRate)
	taxRates.PUT("/:id", UpdateTaxRate)
	taxRates.DELETE("/:id", DeleteTaxRate)

	t.Run("Create Tax Rate", func(t *testing.T) {
		taxRateData := models.TaxRateRequest{
			Name:        "VAT",
			Code:        stringPtr("VAT15"),
			Type:        stringPtr("percentage"),
			Amount:      15.0,
			Active:      true,
			Description: stringPtr("Value Added Tax 15%"),
		}

		jsonData, _ := json.Marshal(taxRateData)
		req, _ := http.NewRequest("POST", "/api/v1/backend/tax_rates", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Tax rate created successfully", response["message"])
		assert.NotNil(t, response["data"])
	})

	t.Run("Get All Tax Rates", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/backend/tax_rates", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.NotNil(t, response["data"])
	})

	t.Run("Search Tax Rates", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/backend/tax_rates/search?search=VAT", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.NotNil(t, response["data"])
	})

	t.Run("Get Tax Rate By ID", func(t *testing.T) {
		// First create a tax rate
		taxRate := models.TaxRate{
			Name:        "GST",
			Code:        stringPtr("GST10"),
			Type:        stringPtr("percentage"),
			Amount:      10.0,
			Active:      true,
			Description: stringPtr("Goods and Services Tax 10%"),
		}
		config.DB.Create(&taxRate)

		req, _ := http.NewRequest("GET", "/api/v1/backend/tax_rates/"+strconv.Itoa(int(taxRate.Id)), nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.NotNil(t, response["data"])
	})

	t.Run("Update Tax Rate", func(t *testing.T) {
		// First create a tax rate
		taxRate := models.TaxRate{
			Name:        "Sales Tax",
			Code:        stringPtr("ST5"),
			Type:        stringPtr("percentage"),
			Amount:      5.0,
			Active:      true,
			Description: stringPtr("Sales Tax 5%"),
		}
		config.DB.Create(&taxRate)

		updateData := models.TaxRateRequest{
			Name:        "Updated Sales Tax",
			Code:        stringPtr("ST7"),
			Type:        stringPtr("percentage"),
			Amount:      7.0,
			Active:      true,
			Description: stringPtr("Updated Sales Tax 7%"),
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest("PUT", "/api/v1/backend/tax_rates/"+strconv.Itoa(int(taxRate.Id)), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Tax rate updated successfully", response["message"])
	})

	t.Run("Delete Tax Rate", func(t *testing.T) {
		// First create a tax rate
		taxRate := models.TaxRate{
			Name:        "Temporary Tax",
			Code:        stringPtr("TEMP"),
			Type:        stringPtr("percentage"),
			Amount:      1.0,
			Active:      true,
			Description: stringPtr("Temporary tax for testing"),
		}
		config.DB.Create(&taxRate)

		req, _ := http.NewRequest("DELETE", "/api/v1/backend/tax_rates/"+strconv.Itoa(int(taxRate.Id)), nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Tax rate deleted successfully", response["message"])
	})
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
