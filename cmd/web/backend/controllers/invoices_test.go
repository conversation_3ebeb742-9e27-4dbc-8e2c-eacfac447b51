package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupInvoiceTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.<PERSON>env("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()

	gin.SetMode(gin.TestMode)

	// Clean up existing test data
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM currencies")
	config.DB.Exec("DELETE FROM payment_types")
}

func TestCreateSubscriptionInvoice(t *testing.T) {
	setupInvoiceTest()
	
	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// Add test routes
	v1 := router.Group("/api/v1/backend")
	invoices := v1.Group("/invoices")
	invoices.POST("", CreateInvoice)
	
	// Create test currency
	currency := models.Currency{
		Name:   "USD",
		Symbol: "$",
		Code:   "USD",
	}
	config.DB.Create(&currency)
	
	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Bank Transfer",
		Active: true,
	}
	config.DB.Create(&paymentType)
	
	// Create test client
	client := models.Client{
		Name:         "Test Client",
		Email:        "<EMAIL>",
		PhoneNumber:  "**********",
		ClientType:   "individual",
		Status:       "active",
		BillingCycle: func() *string { s := "monthly"; return &s }(),
		BillingDay:   func() *uint { d := uint(15); return &d }(),
		CurrencyId:   &currency.Id,
	}
	config.DB.Create(&client)
	
	t.Run("Create Subscription Invoice Without NextBillingDate", func(t *testing.T) {
		invoiceDate := time.Now()
		dueDate := invoiceDate.AddDate(0, 0, 15)
		amount := 100.0
		isSubscription := true
		
		invoiceData := map[string]interface{}{
			"client_id":       client.Id,
			"currency_id":     currency.Id,
			"payment_type_id": paymentType.Id,
			"date":            invoiceDate.Format("2006-01-02T15:04:05Z"),
			"due_date":        dueDate.Format("2006-01-02T15:04:05Z"),
			"amount":          amount,
			"status":          "draft",
			"is_subscription": isSubscription,
		}
		
		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest("POST", "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Invoice created successfully", response["message"])
		
		// Verify client's NextBillingDate was set
		var updatedClient models.Client
		config.DB.First(&updatedClient, client.Id)
		assert.NotNil(t, updatedClient.NextBillingDate)
		
		// Verify the next billing date is calculated correctly (should be next month on the 15th)
		expectedNextBilling := calculateNextBillingDate(invoiceDate, "monthly", 15)
		assert.Equal(t, expectedNextBilling.Format("2006-01-02"), updatedClient.NextBillingDate.Format("2006-01-02"))
	})
	
	t.Run("Create Subscription Invoice With NextBillingDate", func(t *testing.T) {
		// Reset client's NextBillingDate
		config.DB.Model(&client).Update("next_billing_date", nil)
		
		invoiceDate := time.Now()
		dueDate := invoiceDate.AddDate(0, 0, 15)
		amount := 100.0
		isSubscription := true
		customNextBilling := time.Now().AddDate(0, 2, 0) // 2 months from now
		
		invoiceData := map[string]interface{}{
			"client_id":         client.Id,
			"currency_id":       currency.Id,
			"payment_type_id":   paymentType.Id,
			"date":              invoiceDate.Format("2006-01-02T15:04:05Z"),
			"due_date":          dueDate.Format("2006-01-02T15:04:05Z"),
			"amount":            amount,
			"status":            "draft",
			"is_subscription":   isSubscription,
			"next_billing_date": customNextBilling.Format("2006-01-02T15:04:05Z"),
		}
		
		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest("POST", "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Invoice created successfully", response["message"])
		
		// Verify client's NextBillingDate was set to the custom date
		var updatedClient models.Client
		config.DB.First(&updatedClient, client.Id)
		assert.NotNil(t, updatedClient.NextBillingDate)
		assert.Equal(t, customNextBilling.Format("2006-01-02"), updatedClient.NextBillingDate.Format("2006-01-02"))
	})
	
	t.Run("Create Non-Subscription Invoice", func(t *testing.T) {
		// Reset client's NextBillingDate
		config.DB.Model(&client).Update("next_billing_date", nil)
		
		invoiceDate := time.Now()
		dueDate := invoiceDate.AddDate(0, 0, 15)
		amount := 100.0
		isSubscription := false
		
		invoiceData := map[string]interface{}{
			"client_id":       client.Id,
			"currency_id":     currency.Id,
			"payment_type_id": paymentType.Id,
			"date":            invoiceDate.Format("2006-01-02T15:04:05Z"),
			"due_date":        dueDate.Format("2006-01-02T15:04:05Z"),
			"amount":          amount,
			"status":          "draft",
			"is_subscription": isSubscription,
		}
		
		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest("POST", "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Invoice created successfully", response["message"])
		
		// Verify client's NextBillingDate was NOT set for non-subscription invoice
		var updatedClient models.Client
		config.DB.First(&updatedClient, client.Id)
		assert.Nil(t, updatedClient.NextBillingDate)
	})
}
