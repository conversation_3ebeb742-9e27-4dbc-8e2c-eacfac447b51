package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllTaxRates(c *gin.Context) {
	var taxRates []models.TaxRate
	filter := map[string]interface{}{}

	// Optional filtering by active status
	if active := c.Query("active"); active != "" {
		if active == "true" {
			filter["active"] = true
		} else if active == "false" {
			filter["active"] = false
		}
	}

	// Optional filtering by type
	if taxType := c.Query("type"); taxType != "" {
		filter["type"] = taxType
	}

	config.DB.Where(filter).Order("id desc").Find(&taxRates)
	c.J<PERSON>N(http.StatusOK, gin.H{
		"data": taxRates,
	})
}

func SearchTaxRates(c *gin.Context) {
	var taxRates []models.TaxRate
	var total int64
	search := c.Query("search")
	filter := map[string]interface{}{}

	// Optional filtering by active status
	if active := c.Query("active"); active != "" {
		if active == "true" {
			filter["active"] = true
		} else if active == "false" {
			filter["active"] = false
		}
	}

	query := config.DB.Where(filter)
	if search != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	query.Scopes(utils.Paginate(c)).Order("id desc").Find(&taxRates)
	query.Model(&models.TaxRate{}).Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"data":  taxRates,
		"total": total,
	})
}

func GetTaxRateById(c *gin.Context) {
	var taxRate models.TaxRate
	if err := config.DB.First(&taxRate, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Tax rate not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": taxRate,
	})
}

func CreateTaxRate(c *gin.Context) {
	var req models.TaxRateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	taxRate := models.TaxRate{
		Name:        req.Name,
		Code:        req.Code,
		Type:        req.Type,
		Amount:      req.Amount,
		Active:      req.Active,
		Description: req.Description,
	}

	// Set default type if not provided
	if taxRate.Type == nil {
		defaultType := "percentage"
		taxRate.Type = &defaultType
	}

	result := config.DB.Create(&taxRate)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Tax rate created successfully",
		"data":    taxRate,
	})
}

func UpdateTaxRate(c *gin.Context) {
	var req models.TaxRateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var taxRate models.TaxRate
	if err := config.DB.First(&taxRate, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Tax rate not found",
		})
		return
	}

	// Update fields
	taxRate.Name = req.Name
	taxRate.Code = req.Code
	taxRate.Type = req.Type
	taxRate.Amount = req.Amount
	taxRate.Active = req.Active
	taxRate.Description = req.Description

	// Set default type if not provided
	if taxRate.Type == nil {
		defaultType := "percentage"
		taxRate.Type = &defaultType
	}

	if err := config.DB.Save(&taxRate).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Tax rate updated successfully",
		"data":    taxRate,
	})
}

func DeleteTaxRate(c *gin.Context) {
	var taxRate models.TaxRate
	if err := config.DB.First(&taxRate, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Tax rate not found",
		})
		return
	}

	// Check if tax rate is being used in any invoices
	var invoiceCount int64
	config.DB.Model(&models.Invoice{}).Where("tax_rate_id = ?", taxRate.Id).Count(&invoiceCount)
	if invoiceCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Cannot delete tax rate as it is being used in invoices",
		})
		return
	}

	// Check if tax rate is being used in any invoice items
	var invoiceItemCount int64
	config.DB.Model(&models.InvoiceItem{}).Where("tax_rate_id = ?", taxRate.Id).Count(&invoiceItemCount)
	if invoiceItemCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Cannot delete tax rate as it is being used in invoice items",
		})
		return
	}

	if err := config.DB.Delete(&taxRate).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Tax rate deleted successfully",
	})
}
