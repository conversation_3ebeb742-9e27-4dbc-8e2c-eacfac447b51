package routes

import (
	"github.com/gin-gonic/gin"
	"yotracker/cmd/web/backend/controllers"
	"yotracker/cmd/web/middleware"
)

func BackendRoutes(hub *controllers.Hub, r *gin.Engine) {
	v1 := r.Group("/api/v1/backend")
	v1.GET("", controllers.Home)
	v1.POST("/login", controllers.Login)
	v1.POST("/register", controllers.Register)
	// Password reset routes (no auth required)
	v1.POST("/password-reset/request", controllers.RequestPasswordReset)
	v1.POST("/password-reset/verify", controllers.VerifyPasswordResetToken)
	v1.POST("/password-reset/confirm", controllers.ResetPassword)
	webhook := v1.Group("/webhook")
	webhook.Any("/paynow", controllers.PaynowWebhook)
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckIfUserIsBackend())
	//websocket
	v1.GET("/ws/:channel", func(c *gin.Context) {
		controllers.HandleWebsocketRequest(hub, c)
	})
	//users
	users := v1.Group("/users")
	users.GET("", controllers.GetAllUsers)
	users.GET("/search", controllers.SearchUsers)
	users.GET("/:id", controllers.GetUserById)
	users.POST("", controllers.CreateUser)
	users.PUT("/:id", controllers.UpdateUser)
	users.DELETE("/:id", controllers.DeleteUser)
	users.GET("/profile", controllers.Profile)
	//roles
	roles := v1.Group("/roles")
	roles.GET("", controllers.GetAllRoles)
	roles.GET("/search", controllers.SearchRoles)
	roles.GET("/:id", controllers.GetRoleById)
	roles.POST("", controllers.CreateRole)
	roles.PUT("/:id", controllers.UpdateRole)
	roles.DELETE("/:id", controllers.DeleteRole)
	//clients
	clients := v1.Group("/clients")
	clients.GET("", controllers.GetAllClients)
	clients.GET("/search", controllers.SearchClients)
	clients.GET("/:id", controllers.GetClientById)
	clients.POST("", controllers.CreateClient)
	clients.PUT("/:id", controllers.UpdateClient)
	clients.DELETE("/:id", controllers.DeleteClient)
	clients.GET("/:id/fleets", controllers.GetAllFleets)
	clients.GET("/fleets/:id", controllers.GetFleetById)
	clients.POST("/:id/fleets", controllers.CreateFleet)
	clients.PUT("/fleets/:id", controllers.UpdateFleet)
	clients.DELETE("/fleets/:id", controllers.DeleteFleet)
	clients.POST("/:id/users", controllers.CreateClientUser)
	clients.PUT("/:id/users", controllers.UpdateClientUser)
	clients.DELETE("/users/:id", controllers.DeleteClientUser)

	//protocols
	protocols := v1.Group("/protocols")
	protocols.GET("", controllers.GetAllProtocols)
	protocols.GET("/search", controllers.SearchProtocols)
	protocols.GET("/:id", controllers.GetProtocolById)
	protocols.POST("", controllers.CreateProtocol)
	protocols.PUT("/:id", controllers.UpdateProtocol)
	protocols.DELETE("/:id", controllers.DeleteProtocol)
	//device types
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("", controllers.GetAllDeviceTypes)
	deviceTypes.GET("/search", controllers.SearchDeviceTypes)
	deviceTypes.GET("/:id", controllers.GetDeviceTypeById)
	deviceTypes.POST("", controllers.CreateDeviceType)
	deviceTypes.PUT("/:id", controllers.UpdateDeviceType)
	deviceTypes.DELETE("/:id", controllers.DeleteDeviceType)
	//client device
	clientDevices := v1.Group("/client_devices")
	clientDevices.GET("", controllers.GetAllClientDevices)
	clientDevices.GET("/search", controllers.SearchClientDevices)
	clientDevices.GET("/:id", controllers.GetClientDeviceById)
	clientDevices.POST("", controllers.CreateClientDevice)
	clientDevices.PUT("/:id", controllers.UpdateClientDevice)
	clientDevices.DELETE("/:id", controllers.DeleteClientDevice)
	clientDevices.POST("/command_log/:id", controllers.CreateClientDevice)
	clientDevices.GET("/command_log/:id", controllers.CreateClientDevice)
	//gps data
	gpsData := v1.Group("/gps_data")
	gpsData.GET("/search", controllers.SearchGpsData)
	gpsData.GET("/last_location", controllers.GetLastLocation)
	gpsData.GET("/:id", controllers.GetGpsDataById)
	gpsData.POST("", controllers.CreateGpsData)
	gpsData.PUT("/:id", controllers.UpdateGpsData)
	gpsData.DELETE("/:id", controllers.DeleteGpsData)
	//alerts
	alerts := v1.Group("/alerts")
	alerts.GET("", controllers.GetAllAlerts)
	alerts.GET("/search", controllers.SearchAlerts)
	alerts.GET("/:id", controllers.GetAlertById)
	alerts.POST("", controllers.CreateAlert)
	alerts.PUT("/:id", controllers.UpdateAlert)
	alerts.DELETE("/:id", controllers.DeleteAlert)
	alerts.PATCH("/:id/mark-read", controllers.MarkAlertAsRead)
	alerts.PATCH("/:id/mark-unread", controllers.MarkAlertAsUnread)
	alerts.PATCH("/bulk/mark-read", controllers.BulkMarkAlertsAsRead)
	alerts.PATCH("/mark-all-read", controllers.MarkAllAlertsAsRead)
	//command logs
	commandLogs := v1.Group("/command_logs")
	commandLogs.GET("", controllers.GetAllCommandLogs)
	commandLogs.GET("/search", controllers.SearchCommandLogs)
	commandLogs.GET("/:id", controllers.GetCommandLogById)
	commandLogs.POST("", controllers.CreateCommandLog)
	commandLogs.PUT("/:id", controllers.UpdateCommandLog)
	commandLogs.DELETE("/:id", controllers.DeleteCommandLog)
	//invoices
	invoices := v1.Group("/invoices")
	invoices.GET("", controllers.GetAllInvoices)
	invoices.GET("/search", controllers.SearchInvoices)
	invoices.GET("/:id", controllers.GetInvoiceById)
	invoices.POST("", controllers.CreateInvoice)
	invoices.PUT("/:id", controllers.UpdateInvoice)
	invoices.DELETE("/:id", controllers.DeleteInvoice)
	//payments
	payments := v1.Group("/payments")
	payments.GET("", controllers.GetAllPayments)
	payments.GET("/search", controllers.SearchPayments)
	payments.GET("/:id", controllers.GetPaymentById)
	payments.POST("", controllers.CreatePayment)
	payments.PUT("/:id", controllers.UpdatePayment)
	payments.DELETE("/:id", controllers.DeletePayment)
	//payment types
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("", controllers.GetAllPaymentTypes)
	paymentTypes.GET("/search", controllers.SearchPaymentTypes)
	paymentTypes.GET("/:id", controllers.GetPaymentTypeById)
	paymentTypes.POST("", controllers.CreatePaymentType)
	paymentTypes.PUT("/:id", controllers.UpdatePaymentType)
	paymentTypes.DELETE("/:id", controllers.DeletePaymentType)
	//currencies
	currencies := v1.Group("/currencies")
	currencies.GET("", controllers.GetAllCurrencies)
	currencies.GET("/search", controllers.SearchCurrencies)
	currencies.GET("/:id", controllers.GetCurrencyById)
	currencies.POST("", controllers.CreateCurrency)
	currencies.PUT("/:id", controllers.UpdateCurrency)
	currencies.DELETE("/:id", controllers.DeleteCurrency)
	//tax rates
	taxRates := v1.Group("/tax_rates")
	taxRates.GET("", controllers.GetAllTaxRates)
	taxRates.GET("/search", controllers.SearchTaxRates)
	taxRates.GET("/:id", controllers.GetTaxRateById)
	taxRates.POST("", controllers.CreateTaxRate)
	taxRates.PUT("/:id", controllers.UpdateTaxRate)
	taxRates.DELETE("/:id", controllers.DeleteTaxRate)
	//countries
	countries := v1.Group("/countries")
	countries.GET("", controllers.GetAllCountries)

	//settings
	settings := v1.Group("/settings")
	settings.GET("", controllers.GetAllSettings)
	settings.GET("/allowed", controllers.GetAllowedSettings)
	settings.GET("/:key", controllers.GetSettingByKey)
	settings.GET("/:key/value", controllers.GetSettingValue)
	settings.POST("", controllers.CreateSetting)
	settings.PUT("/:key", controllers.UpdateSetting)
	settings.DELETE("/:key", controllers.DeleteSetting)

	//dashboard
	dashboard := v1.Group("/dashboard")
	dashboard.GET("/dashboard_stats", controllers.GetDashboardStats)
	dashboard.GET("/server_stats", controllers.GetServerStats)

}
